{% extends "base.html" %}

{% block title %}Edit {{ classroom.name }} - Admin Dashboard{% endblock %}

{% block content %}
<div class="container">
    <!-- Breadcrumb -->
    <nav style="margin-bottom: 2rem;">
        <ol style="list-style: none; padding: 0; display: flex; gap: 10px; color: #7f8c8d;">
            <li><a href="{{ url_for('home') }}" style="color: #3498db; text-decoration: none;">Home</a></li>
            <li>::</li>
            <li><a href="{{ url_for('admin_dashboard') }}" style="color: #3498db; text-decoration: none;">Admin Dashboard</a></li>
            <li>::</li>
            <li style="color: #2c3e50;">Edit {{ classroom.name }}</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Form Section -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">Edit Classroom</h1>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('edit_classroom', classroom_id=classroom.id) }}">
                        <!-- Basic Information -->
                        <div class="form-section" style="margin-bottom: 2rem;">
                            <h3 style="color: #2c3e50; margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid #ecf0f1;">
                                <i class="icon-info"></i> Basic Information
                            </h3>
                            
                            <div class="form-group">
                                <label for="name">Classroom Name *</label>
                                <input type="text" class="form-control" id="name" name="name" required value="{{ classroom.name }}" placeholder="e.g., Lecture Hall A, Computer Lab 1">
                                <small class="form-text text-muted">Enter a descriptive name for the classroom</small>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="building_name">Building Name *</label>
                                        <input type="text" class="form-control" id="building_name" name="building_name" required value="{{ classroom.building_name }}" placeholder="e.g., Main Building, Technology Building">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="capacity">Capacity *</label>
                                        <input type="number" class="form-control" id="capacity" name="capacity" required min="1" max="500" value="{{ classroom.capacity }}" placeholder="e.g., 30">
                                        <small class="form-text text-muted">Maximum number of people</small>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="location">Location/Room Number</label>
                                <input type="text" class="form-control" id="location" name="location" value="{{ classroom.location or '' }}" placeholder="e.g., Building A, Room 101">
                                <small class="form-text text-muted">Specific location within the building (optional)</small>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="form-section" style="margin-bottom: 2rem;">
                            <h3 style="color: #2c3e50; margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid #ecf0f1;">
                                <i class="icon-description"></i> Description & Features
                            </h3>
                            
                            <div class="form-group">
                                <label for="description">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="4" placeholder="Describe the classroom features, equipment, and facilities...">{{ classroom.description or '' }}</textarea>
                                <small class="form-text text-muted">Provide details about equipment, layout, and special features</small>
                            </div>
                        </div>

                        <!-- Settings -->
                        <div class="form-section" style="margin-bottom: 2rem;">
                            <h3 style="color: #2c3e50; margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid #ecf0f1;">
                                <i class="icon-settings"></i> Settings
                            </h3>
                            
                            <div class="form-group">
                                <div class="form-check" style="padding: 15px; background: #f8f9fa; border-radius: 8px;">
                                    <input type="checkbox" class="form-check-input" id="is_featured" name="is_featured" style="margin-right: 10px;" {% if classroom.is_featured %}checked{% endif %}>
                                    <label class="form-check-label" for="is_featured" style="font-weight: 500;">
                                        <i class="icon-featured"></i> Featured Classroom
                                    </label>
                                    <small class="form-text text-muted" style="display: block; margin-top: 5px;">
                                        Featured classrooms appear on the home page and get priority in listings
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="form-actions" style="text-align: center; padding-top: 2rem; border-top: 1px solid #ecf0f1;">
                            <button type="submit" class="btn btn-success" style="font-size: 1.1rem; padding: 12px 30px; margin-right: 1rem;">
                                <i class="icon-save"></i> Update Classroom
                            </button>
                            <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary" style="font-size: 1.1rem; padding: 12px 30px;">
                                <i class="icon-cancel"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Info Section -->
        <div class="col-md-4">
            <!-- Current Info -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="icon-current"></i> Current Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="current-info">
                        <div class="info-item" style="margin-bottom: 1rem; padding-bottom: 1rem; border-bottom: 1px solid #ecf0f1;">
                            <strong style="color: #7f8c8d;">Name:</strong><br>
                            <span style="font-size: 1.1rem;">{{ classroom.name }}</span>
                        </div>
                        
                        <div class="info-item" style="margin-bottom: 1rem; padding-bottom: 1rem; border-bottom: 1px solid #ecf0f1;">
                            <strong style="color: #7f8c8d;">Building:</strong><br>
                            <span style="font-size: 1.1rem;">{{ classroom.building_name }}</span>
                        </div>
                        
                        <div class="info-item" style="margin-bottom: 1rem; padding-bottom: 1rem; border-bottom: 1px solid #ecf0f1;">
                            <strong style="color: #7f8c8d;">Capacity:</strong><br>
                            <span style="font-size: 1.1rem; color: #e74c3c; font-weight: bold;">{{ classroom.capacity }} people</span>
                        </div>
                        
                        {% if classroom.location %}
                        <div class="info-item" style="margin-bottom: 1rem; padding-bottom: 1rem; border-bottom: 1px solid #ecf0f1;">
                            <strong style="color: #7f8c8d;">Location:</strong><br>
                            <span style="font-size: 1.1rem;">{{ classroom.location }}</span>
                        </div>
                        {% endif %}
                        
                        <div class="info-item" style="margin-bottom: 1rem; padding-bottom: 1rem; border-bottom: 1px solid #ecf0f1;">
                            <strong style="color: #7f8c8d;">Featured:</strong><br>
                            {% if classroom.is_featured %}
                                <span style="color: #27ae60; font-weight: bold;">Yes</span>
                            {% else %}
                                <span style="color: #7f8c8d;">No</span>
                            {% endif %}
                        </div>
                        
                        <div class="info-item">
                            <strong style="color: #7f8c8d;">Created:</strong><br>
                            <span style="font-size: 0.9rem;">{{ classroom.created_at.strftime('%B %d, %Y') if classroom.created_at else 'Unknown' }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card" style="margin-top: 1rem;">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="icon-actions"></i> Quick Actions
                    </h3>
                </div>
                <div class="card-body" style="text-align: center;">
                    <a href="{{ url_for('classroom_details', classroom_id=classroom.id) }}" class="btn btn-primary" style="width: 100%; margin-bottom: 10px;">
                        <i class="icon-view"></i> View Classroom
                    </a>
                    <a href="{{ url_for('delete_classroom', classroom_id=classroom.id) }}" class="btn btn-danger delete-btn" style="width: 100%; margin-bottom: 10px;">
                        <i class="icon-delete"></i> Delete Classroom
                    </a>
                    <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary" style="width: 100%;">
                        <i class="icon-back"></i> Back to Dashboard
                    </a>
                </div>
            </div>

            <!-- Warning -->
            <div class="card" style="margin-top: 1rem; border-left: 4px solid #f39c12;">
                <div class="card-body">
                    <h5 style="color: #f39c12; margin-bottom: 1rem;">
                        <i class="icon-warning"></i> Important Notes
                    </h5>
                    <ul style="padding-left: 1.2rem; line-height: 1.6; color: #7f8c8d;">
                        <li>Changes will affect all future bookings</li>
                        <li>Existing bookings will remain unchanged</li>
                        <li>Time slots are not modified when editing</li>
                        <li>Deleting will cancel all future bookings</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.icon-info::before { content: "ℹ️"; margin-right: 8px; }
.icon-description::before { content: "📝"; margin-right: 8px; }
.icon-settings::before { content: "⚙️"; margin-right: 8px; }
.icon-featured::before { content: "⭐"; margin-right: 8px; }
.icon-save::before { content: "💾"; margin-right: 8px; }
.icon-cancel::before { content: "❌"; margin-right: 8px; }
.icon-current::before { content: "📋"; margin-right: 8px; }
.icon-actions::before { content: "⚡"; margin-right: 8px; }
.icon-view::before { content: "👁️"; margin-right: 8px; }
.icon-delete::before { content: "🗑️"; margin-right: 8px; }
.icon-back::before { content: "⬅️"; margin-right: 8px; }
.icon-warning::before { content: "⚠️"; margin-right: 8px; }

.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-check-input:checked {
    background-color: #27ae60;
    border-color: #27ae60;
}

.btn:hover {
    transform: translateY(-1px);
}

@media (max-width: 768px) {
    .form-actions .btn {
        width: 100%;
        margin-bottom: 10px;
        margin-right: 0 !important;
    }
}
</style>
{% endblock %}
