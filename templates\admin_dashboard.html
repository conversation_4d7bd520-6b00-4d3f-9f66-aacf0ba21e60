{% extends "base.html" %}

{% block title %}Admin Dashboard - Classroom Booking System{% endblock %}

{% block content %}
<div class="container">
    <!-- Dashboard Header -->
    <div class="dashboard-header" style="text-align: center; margin-bottom: 3rem;">
        <h1 style="color: #2c3e50; margin-bottom: 0.5rem;">Admin Dashboard</h1>
        <p style="color: #7f8c8d; font-size: 1.1rem;">Manage classrooms and view all bookings</p>
    </div>

    <!-- Quick Stats -->
    <div class="quick-stats" style="margin-bottom: 3rem;">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #3498db, #2980b9); color: white; padding: 2rem; border-radius: 10px; text-align: center;">
                    <h3 style="margin: 0; font-size: 2rem;">{{ classrooms|length }}</h3>
                    <p style="margin: 0; opacity: 0.9;">Total Classrooms</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #27ae60, #229954); color: white; padding: 2rem; border-radius: 10px; text-align: center;">
                    <h3 style="margin: 0; font-size: 2rem;">{{ bookings|length }}</h3>
                    <p style="margin: 0; opacity: 0.9;">Total Bookings</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #f39c12, #e67e22); color: white; padding: 2rem; border-radius: 10px; text-align: center;">
                    <h3 style="margin: 0; font-size: 2rem;">
                        {% set today_bookings = bookings|selectattr('booking_date', 'eq', today)|list|length %}
                        {{ today_bookings }}
                    </h3>
                    <p style="margin: 0; opacity: 0.9;">Today's Bookings</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white; padding: 2rem; border-radius: 10px; text-align: center;">
                    <h3 style="margin: 0; font-size: 2rem;">
                        {% set featured_count = classrooms|selectattr('is_featured', 'equalto', true)|list|length %}
                        {{ featured_count }}
                    </h3>
                    <p style="margin: 0; opacity: 0.9;">Featured Rooms</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Manage Classrooms Section -->
    <div class="manage-classrooms" style="margin-bottom: 3rem;">
        <div class="section-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
            <h2 style="color: #2c3e50; margin: 0; padding-bottom: 0.5rem; border-bottom: 3px solid #3498db;">
                <i class="icon-classroom"></i> Manage Classrooms
            </h2>
            <a href="{{ url_for('add_classroom') }}" class="btn btn-success" style="font-size: 1.1rem; padding: 10px 20px;">
                <i class="icon-add"></i> Add New Classroom
            </a>
        </div>

        {% if classrooms %}
        <div class="classrooms-table" style="background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden;">
            <div class="table-responsive">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead style="background: #f8f9fa;">
                        <tr>
                            <th style="padding: 15px; text-align: left; font-weight: 600; color: #2c3e50; border-bottom: 1px solid #ecf0f1;">Name</th>
                            <th style="padding: 15px; text-align: left; font-weight: 600; color: #2c3e50; border-bottom: 1px solid #ecf0f1;">Building</th>
                            <th style="padding: 15px; text-align: center; font-weight: 600; color: #2c3e50; border-bottom: 1px solid #ecf0f1;">Capacity</th>
                            <th style="padding: 15px; text-align: center; font-weight: 600; color: #2c3e50; border-bottom: 1px solid #ecf0f1;">Featured</th>
                            <th style="padding: 15px; text-align: center; font-weight: 600; color: #2c3e50; border-bottom: 1px solid #ecf0f1;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for classroom in classrooms %}
                        <tr style="border-bottom: 1px solid #ecf0f1; transition: background-color 0.3s ease;">
                            <td style="padding: 15px; font-weight: 500;">{{ classroom.name }}</td>
                            <td style="padding: 15px; color: #7f8c8d;">{{ classroom.building_name }}</td>
                            <td style="padding: 15px; text-align: center; font-weight: bold; color: #e74c3c;">{{ classroom.capacity }}</td>
                            <td style="padding: 15px; text-align: center;">
                                {% if classroom.is_featured %}
                                    <span style="background: #e8f5e8; color: #27ae60; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem;">Yes</span>
                                {% else %}
                                    <span style="background: #f8f9fa; color: #7f8c8d; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem;">No</span>
                                {% endif %}
                            </td>
                            <td style="padding: 15px; text-align: center;">
                                <a href="{{ url_for('classroom_details', classroom_id=classroom.id) }}" class="btn btn-primary" style="margin-right: 5px; font-size: 0.9rem; padding: 5px 10px;">View</a>
                                <a href="{{ url_for('edit_classroom', classroom_id=classroom.id) }}" class="btn btn-secondary" style="margin-right: 5px; font-size: 0.9rem; padding: 5px 10px;">Edit</a>
                                <a href="{{ url_for('delete_classroom', classroom_id=classroom.id) }}" class="btn btn-danger delete-btn" style="font-size: 0.9rem; padding: 5px 10px;">Delete</a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% else %}
        <div class="no-classrooms" style="text-align: center; padding: 3rem; background: #f8f9fa; border-radius: 10px;">
            <div style="font-size: 4rem; margin-bottom: 1rem;">🏫</div>
            <h3 style="color: #7f8c8d; margin-bottom: 1rem;">No Classrooms</h3>
            <p style="color: #95a5a6; margin-bottom: 2rem;">Start by adding your first classroom.</p>
            <a href="{{ url_for('add_classroom') }}" class="btn btn-success">Add Classroom</a>
        </div>
        {% endif %}
    </div>

    <!-- View Bookings Section -->
    <div class="view-bookings">
        <h2 style="color: #2c3e50; margin-bottom: 2rem; padding-bottom: 0.5rem; border-bottom: 3px solid #27ae60;">
            <i class="icon-bookings"></i> View Bookings
        </h2>

        {% if bookings %}
        <div class="bookings-table" style="background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden;">
            <div class="table-responsive">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead style="background: #f8f9fa;">
                        <tr>
                            <th style="padding: 15px; text-align: left; font-weight: 600; color: #2c3e50; border-bottom: 1px solid #ecf0f1;">User</th>
                            <th style="padding: 15px; text-align: left; font-weight: 600; color: #2c3e50; border-bottom: 1px solid #ecf0f1;">Classroom</th>
                            <th style="padding: 15px; text-align: left; font-weight: 600; color: #2c3e50; border-bottom: 1px solid #ecf0f1;">Event</th>
                            <th style="padding: 15px; text-align: center; font-weight: 600; color: #2c3e50; border-bottom: 1px solid #ecf0f1;">Date</th>
                            <th style="padding: 15px; text-align: center; font-weight: 600; color: #2c3e50; border-bottom: 1px solid #ecf0f1;">Time</th>
                            <th style="padding: 15px; text-align: center; font-weight: 600; color: #2c3e50; border-bottom: 1px solid #ecf0f1;">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for booking in bookings[:20] %}
                        <tr style="border-bottom: 1px solid #ecf0f1;">
                            <td style="padding: 15px; font-weight: 500;">{{ booking.user_name }}</td>
                            <td style="padding: 15px; color: #3498db;">{{ booking.classroom_name }}</td>
                            <td style="padding: 15px; color: #7f8c8d;">{{ booking.event_name }}</td>
                            <td style="padding: 15px; text-align: center; font-weight: 500;">
                                {{ booking.booking_date.strftime('%Y-%m-%d') }}
                            </td>
                            <td style="padding: 15px; text-align: center; color: #27ae60; font-weight: 500;">
                                {{ booking.start_time.strftime('%H:%M') }} - {{ booking.end_time.strftime('%H:%M') }}
                            </td>
                            <td style="padding: 15px; text-align: center;">
                                {% if booking.status == 'confirmed' %}
                                    <span style="background: #e8f5e8; color: #27ae60; padding: 4px 12px; border-radius: 12px; font-size: 0.8rem;">Confirmed</span>
                                {% else %}
                                    <span style="background: #fdf2f2; color: #e74c3c; padding: 4px 12px; border-radius: 12px; font-size: 0.8rem;">Cancelled</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% if bookings|length > 20 %}
            <div style="padding: 15px; text-align: center; background: #f8f9fa; border-top: 1px solid #ecf0f1;">
                <small style="color: #7f8c8d;">Showing latest 20 bookings out of {{ bookings|length }} total</small>
            </div>
            {% endif %}
        </div>
        {% else %}
        <div class="no-bookings" style="text-align: center; padding: 3rem; background: #f8f9fa; border-radius: 10px;">
            <div style="font-size: 4rem; margin-bottom: 1rem;">📅</div>
            <h3 style="color: #7f8c8d; margin-bottom: 1rem;">No Bookings</h3>
            <p style="color: #95a5a6;">No bookings have been made yet.</p>
        </div>
        {% endif %}
    </div>
</div>

<style>
.icon-classroom::before { content: "🏫"; margin-right: 8px; }
.icon-add::before { content: "➕"; margin-right: 8px; }
.icon-bookings::before { content: "📋"; margin-right: 8px; }

.stat-card {
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.classrooms-table tbody tr:hover,
.bookings-table tbody tr:hover {
    background-color: #f8f9fa;
}

.table-responsive {
    overflow-x: auto;
}

@media (max-width: 768px) {
    .section-header {
        flex-direction: column !important;
        gap: 1rem;
    }
    
    .section-header h2 {
        text-align: center;
    }
    
    table {
        font-size: 0.9rem;
    }
    
    th, td {
        padding: 10px 8px !important;
    }
    
    .btn {
        font-size: 0.8rem !important;
        padding: 4px 8px !important;
        margin: 2px !important;
    }
}
</style>
{% endblock %}
