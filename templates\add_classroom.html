{% extends "base.html" %}

{% block title %}Add New Classroom - Admin Dashboard{% endblock %}

{% block content %}
<div class="container">
    <!-- Breadcrumb -->
    <nav style="margin-bottom: 2rem;">
        <ol style="list-style: none; padding: 0; display: flex; gap: 10px; color: #7f8c8d;">
            <li><a href="{{ url_for('home') }}" style="color: #3498db; text-decoration: none;">Home</a></li>
            <li>::</li>
            <li><a href="{{ url_for('admin_dashboard') }}" style="color: #3498db; text-decoration: none;">Admin Dashboard</a></li>
            <li>::</li>
            <li style="color: #2c3e50;">Add New Classroom</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Form Section -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">Add New Classroom</h1>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('add_classroom') }}">
                        <!-- Basic Information -->
                        <div class="form-section" style="margin-bottom: 2rem;">
                            <h3 style="color: #2c3e50; margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid #ecf0f1;">
                                <i class="icon-info"></i> Basic Information
                            </h3>
                            
                            <div class="form-group">
                                <label for="name">Classroom Name *</label>
                                <input type="text" class="form-control" id="name" name="name" required placeholder="e.g., Lecture Hall A, Computer Lab 1">
                                <small class="form-text text-muted">Enter a descriptive name for the classroom</small>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="building_name">Building Name *</label>
                                        <input type="text" class="form-control" id="building_name" name="building_name" required placeholder="e.g., Main Building, Technology Building">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="capacity">Capacity *</label>
                                        <input type="number" class="form-control" id="capacity" name="capacity" required min="1" max="500" placeholder="e.g., 30">
                                        <small class="form-text text-muted">Maximum number of people</small>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="location">Location/Room Number</label>
                                <input type="text" class="form-control" id="location" name="location" placeholder="e.g., Building A, Room 101">
                                <small class="form-text text-muted">Specific location within the building (optional)</small>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="form-section" style="margin-bottom: 2rem;">
                            <h3 style="color: #2c3e50; margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid #ecf0f1;">
                                <i class="icon-description"></i> Description & Features
                            </h3>
                            
                            <div class="form-group">
                                <label for="description">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="4" placeholder="Describe the classroom features, equipment, and facilities..."></textarea>
                                <small class="form-text text-muted">Provide details about equipment, layout, and special features</small>
                            </div>
                        </div>

                        <!-- Settings -->
                        <div class="form-section" style="margin-bottom: 2rem;">
                            <h3 style="color: #2c3e50; margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid #ecf0f1;">
                                <i class="icon-settings"></i> Settings
                            </h3>
                            
                            <div class="form-group">
                                <div class="form-check" style="padding: 15px; background: #f8f9fa; border-radius: 8px;">
                                    <input type="checkbox" class="form-check-input" id="is_featured" name="is_featured" style="margin-right: 10px;">
                                    <label class="form-check-label" for="is_featured" style="font-weight: 500;">
                                        <i class="icon-featured"></i> Featured Classroom
                                    </label>
                                    <small class="form-text text-muted" style="display: block; margin-top: 5px;">
                                        Featured classrooms appear on the home page and get priority in listings
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="form-actions" style="text-align: center; padding-top: 2rem; border-top: 1px solid #ecf0f1;">
                            <button type="submit" class="btn btn-success" style="font-size: 1.1rem; padding: 12px 30px; margin-right: 1rem;">
                                <i class="icon-save"></i> Save Classroom
                            </button>
                            <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary" style="font-size: 1.1rem; padding: 12px 30px;">
                                <i class="icon-cancel"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Help Section -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="icon-help"></i> Help & Guidelines
                    </h3>
                </div>
                <div class="card-body">
                    <div class="help-section">
                        <h5 style="color: #3498db; margin-bottom: 1rem;">Naming Guidelines</h5>
                        <ul style="padding-left: 1.2rem; line-height: 1.6; margin-bottom: 2rem;">
                            <li>Use descriptive names (e.g., "Computer Lab 1" instead of "Room 101")</li>
                            <li>Include the type of room when relevant</li>
                            <li>Keep names concise but informative</li>
                        </ul>

                        <h5 style="color: #27ae60; margin-bottom: 1rem;">Capacity Planning</h5>
                        <ul style="padding-left: 1.2rem; line-height: 1.6; margin-bottom: 2rem;">
                            <li>Consider the actual usable space</li>
                            <li>Account for equipment and furniture</li>
                            <li>Follow safety regulations for maximum occupancy</li>
                        </ul>

                        <h5 style="color: #e74c3c; margin-bottom: 1rem;">Featured Classrooms</h5>
                        <ul style="padding-left: 1.2rem; line-height: 1.6;">
                            <li>Limit to 4-6 featured classrooms</li>
                            <li>Choose popular or special-purpose rooms</li>
                            <li>Update featured status regularly</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Preview -->
            <div class="card" style="margin-top: 1rem;">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="icon-preview"></i> Default Time Slots
                    </h3>
                </div>
                <div class="card-body">
                    <p style="margin-bottom: 1rem; color: #7f8c8d;">The following time slots will be automatically created:</p>
                    <div class="time-slots-preview" style="background: #f8f9fa; padding: 1rem; border-radius: 8px;">
                        <strong>Monday - Friday:</strong><br>
                        <div style="margin-top: 0.5rem; line-height: 1.8;">
                            <span class="time-slot" style="background: #27ae60; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8rem; margin-right: 5px; margin-bottom: 5px; display: inline-block;">09:00 - 10:30</span>
                            <span class="time-slot" style="background: #27ae60; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8rem; margin-right: 5px; margin-bottom: 5px; display: inline-block;">10:45 - 12:15</span>
                            <span class="time-slot" style="background: #27ae60; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8rem; margin-right: 5px; margin-bottom: 5px; display: inline-block;">13:30 - 15:00</span>
                            <span class="time-slot" style="background: #27ae60; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8rem; margin-right: 5px; margin-bottom: 5px; display: inline-block;">15:15 - 16:45</span>
                            <span class="time-slot" style="background: #27ae60; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8rem; margin-right: 5px; margin-bottom: 5px; display: inline-block;">17:00 - 18:30</span>
                        </div>
                    </div>
                    <small style="color: #7f8c8d; margin-top: 1rem; display: block;">You can modify these later if needed</small>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.icon-info::before { content: "ℹ️"; margin-right: 8px; }
.icon-description::before { content: "📝"; margin-right: 8px; }
.icon-settings::before { content: "⚙️"; margin-right: 8px; }
.icon-featured::before { content: "⭐"; margin-right: 8px; }
.icon-save::before { content: "💾"; margin-right: 8px; }
.icon-cancel::before { content: "❌"; margin-right: 8px; }
.icon-help::before { content: "❓"; margin-right: 8px; }
.icon-preview::before { content: "👁️"; margin-right: 8px; }

.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-check-input:checked {
    background-color: #27ae60;
    border-color: #27ae60;
}

.btn:hover {
    transform: translateY(-1px);
}

@media (max-width: 768px) {
    .form-actions .btn {
        width: 100%;
        margin-bottom: 10px;
        margin-right: 0 !important;
    }
}
</style>
{% endblock %}
