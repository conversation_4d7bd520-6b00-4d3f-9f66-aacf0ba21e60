<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Classroom Booking System{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <!-- Header with Navigation -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <img src="{{ url_for('static', filename='images/logo.svg') }}" alt="University Logo" class="logo">
                <h1>Classroom Booking System</h1>
            </div>
            
            {% if session.user_id %}
            <nav class="navigation">
                <ul>
                    <li><a href="{{ url_for('home') }}">Home</a></li>
                    <li><a href="{{ url_for('classrooms') }}">Classrooms</a></li>
                    {% if session.is_admin %}
                        <li><a href="{{ url_for('admin_dashboard') }}">Admin Dashboard</a></li>
                    {% else %}
                        <li><a href="{{ url_for('user_dashboard') }}">My Bookings</a></li>
                    {% endif %}
                    <li><a href="{{ url_for('logout') }}">Logout</a></li>
                </ul>
            </nav>
            {% else %}
            <nav class="navigation">
                <ul>
                    <li><a href="{{ url_for('home') }}">Home</a></li>
                    <li><a href="{{ url_for('classrooms') }}">Classrooms</a></li>
                    <li><a href="{{ url_for('login') }}">Login</a></li>
                    <li><a href="{{ url_for('signup') }}">Sign Up</a></li>
                </ul>
            </nav>
            {% endif %}
        </div>
    </header>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-messages">
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">
                        {{ message }}
                        <button type="button" class="close-btn" onclick="this.parentElement.style.display='none'">&times;</button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main class="main-content">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 University Campus - Classroom Booking System</p>
        </div>
    </footer>

    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
