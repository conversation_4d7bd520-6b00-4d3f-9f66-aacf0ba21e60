{% extends "base.html" %}

{% block title %}{{ classroom.name }} - Classroom Details{% endblock %}

{% block content %}
<div class="container">
    <!-- Breadcrumb -->
    <nav style="margin-bottom: 2rem;">
        <ol style="list-style: none; padding: 0; display: flex; gap: 10px; color: #7f8c8d;">
            <li><a href="{{ url_for('home') }}" style="color: #3498db; text-decoration: none;">Home</a></li>
            <li>::</li>
            <li><a href="{{ url_for('classrooms') }}" style="color: #3498db; text-decoration: none;">Classrooms</a></li>
            <li>::</li>
            <li style="color: #2c3e50;">{{ classroom.name }}</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Main Content -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <h1 style="color: #2c3e50; margin: 0;">{{ classroom.name }}</h1>
                        {% if classroom.is_featured %}
                        <span class="featured-badge" style="background: #e8f5e8; color: #27ae60; padding: 8px 15px; border-radius: 20px; font-size: 0.9rem;">Featured Classroom</span>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <!-- Location Information -->
                    <div class="location-info" style="background: #f8f9fa; padding: 1.5rem; border-radius: 8px; margin-bottom: 2rem;">
                        <h3 style="color: #2c3e50; margin-bottom: 1rem;">
                            <i class="icon-location"></i> Location
                        </h3>
                        <p style="font-size: 1.1rem; margin-bottom: 0.5rem;">
                            <strong>Building:</strong> {{ classroom.building_name }}
                        </p>
                        {% if classroom.location %}
                        <p style="font-size: 1.1rem; margin: 0;">
                            <strong>Room:</strong> {{ classroom.location }}
                        </p>
                        {% endif %}
                    </div>

                    <!-- Capacity Information -->
                    <div class="capacity-info" style="background: #e8f4fd; padding: 1.5rem; border-radius: 8px; margin-bottom: 2rem;">
                        <h3 style="color: #2c3e50; margin-bottom: 1rem;">
                            <i class="icon-capacity"></i> Capacity
                        </h3>
                        <p style="font-size: 1.3rem; color: #e74c3c; font-weight: bold; margin: 0;">
                            {{ classroom.capacity }} people
                        </p>
                    </div>

                    <!-- Description -->
                    {% if classroom.description %}
                    <div class="description" style="margin-bottom: 2rem;">
                        <h3 style="color: #2c3e50; margin-bottom: 1rem;">Description</h3>
                        <p style="font-size: 1.1rem; line-height: 1.6; color: #555;">{{ classroom.description }}</p>
                    </div>
                    {% endif %}

                    <!-- Available Time Slots -->
                    {% if time_slots %}
                    <div class="time-slots-section">
                        <h3 style="color: #2c3e50; margin-bottom: 1.5rem;">Available Time Slots</h3>
                        <div class="time-slots-grid">
                            {% set current_day = '' %}
                            {% for slot in time_slots %}
                                {% if slot.day_of_week != current_day %}
                                    {% if current_day != '' %}
                                        </div>
                                    {% endif %}
                                    {% set current_day = slot.day_of_week %}
                                    <div class="day-section" style="margin-bottom: 1.5rem;">
                                        <h4 style="color: #3498db; margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid #ecf0f1;">{{ slot.day_of_week }}</h4>
                                        <div class="slots-row" style="display: flex; flex-wrap: wrap; gap: 10px;">
                                {% endif %}
                                <span class="time-slot" style="background: #27ae60; color: white; padding: 8px 12px; border-radius: 20px; font-size: 0.9rem; white-space: nowrap;">
                                    {{ slot.start_time.strftime('%H:%M') }} - {{ slot.end_time.strftime('%H:%M') }}
                                </span>
                            {% endfor %}
                            {% if time_slots %}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Quick Actions</h3>
                </div>
                <div class="card-body" style="text-align: center;">
                    {% if session.user_id %}
                        <a href="{{ url_for('book_classroom', classroom_id=classroom.id) }}" class="btn btn-success" style="width: 100%; margin-bottom: 1rem; font-size: 1.1rem; padding: 12px;">
                            <i class="icon-book"></i> Book Now
                        </a>
                    {% else %}
                        <p style="margin-bottom: 1rem; color: #7f8c8d;">Please log in to book this classroom</p>
                        <a href="{{ url_for('login') }}" class="btn btn-primary" style="width: 100%; margin-bottom: 1rem;">Login</a>
                        <a href="{{ url_for('signup') }}" class="btn btn-secondary" style="width: 100%; margin-bottom: 1rem;">Sign Up</a>
                    {% endif %}
                    
                    <a href="{{ url_for('classrooms') }}" class="btn btn-secondary" style="width: 100%;">
                        <i class="icon-back"></i> Back to Classrooms
                    </a>
                </div>
            </div>

            <!-- Classroom Stats -->
            <div class="card" style="margin-top: 1rem;">
                <div class="card-header">
                    <h3 class="card-title">Classroom Information</h3>
                </div>
                <div class="card-body">
                    <div class="stat-item" style="display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #ecf0f1;">
                        <span style="color: #7f8c8d;">Building:</span>
                        <span style="font-weight: bold;">{{ classroom.building_name }}</span>
                    </div>
                    <div class="stat-item" style="display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #ecf0f1;">
                        <span style="color: #7f8c8d;">Capacity:</span>
                        <span style="font-weight: bold; color: #e74c3c;">{{ classroom.capacity }} people</span>
                    </div>
                    <div class="stat-item" style="display: flex; justify-content: space-between; padding: 10px 0;">
                        <span style="color: #7f8c8d;">Status:</span>
                        <span style="color: #27ae60; font-weight: bold;">Available</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.icon-location::before { content: "📍"; margin-right: 8px; }
.icon-capacity::before { content: "👥"; margin-right: 8px; }
.icon-book::before { content: "📅"; margin-right: 8px; }
.icon-back::before { content: "⬅️"; margin-right: 8px; }

.time-slot {
    transition: all 0.3s ease;
}

.time-slot:hover {
    background: #229954 !important;
    transform: scale(1.05);
}

.featured-badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@media (max-width: 768px) {
    .slots-row {
        justify-content: center;
    }
    
    .time-slot {
        font-size: 0.8rem !important;
        padding: 6px 10px !important;
    }
}
</style>
{% endblock %}
