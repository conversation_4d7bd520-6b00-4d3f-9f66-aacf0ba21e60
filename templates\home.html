{% extends "base.html" %}

{% block title %}Home - Classroom Booking System{% endblock %}

{% block content %}
<div class="container">
    <!-- Hero Section -->
    <div class="hero-section" style="text-align: center; padding: 3rem 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; margin-bottom: 3rem;">
        <h1 style="font-size: 3rem; margin-bottom: 1rem;">Book Your Favourite Classroom Online!</h1>
        <p style="font-size: 1.2rem; margin-bottom: 2rem;">Simplify your classroom booking experience with our easy-to-use system</p>
        <a href="{{ url_for('classrooms') }}" class="btn btn-primary" style="font-size: 1.1rem; padding: 12px 30px;">View Classrooms</a>
    </div>

    <!-- Featured Classrooms Section -->
    <div class="featured-section">
        <h2 style="text-align: center; margin-bottom: 2rem; color: #2c3e50;">Featured Classrooms</h2>
        
        {% if featured_classrooms %}
        <div class="row">
            {% for classroom in featured_classrooms %}
            <div class="col-md-6" style="margin-bottom: 2rem;">
                <div class="card classroom-card" style="height: 100%; transition: transform 0.3s ease; cursor: pointer;" onclick="location.href='{{ url_for('classroom_details', classroom_id=classroom.id) }}'">
                    <div class="card-body">
                        <h4 class="classroom-name" style="color: #3498db; margin-bottom: 0.5rem;">{{ classroom.name }}</h4>
                        <p class="building-name" style="color: #7f8c8d; margin-bottom: 1rem;">
                            <i class="icon-location"></i> {{ classroom.building_name }}
                        </p>
                        <p style="margin-bottom: 1rem;">{{ classroom.description }}</p>
                        
                        <div class="classroom-info" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                            <span style="background: #ecf0f1; padding: 5px 10px; border-radius: 15px; font-size: 0.9rem;">
                                <strong>Capacity:</strong> {{ classroom.capacity }} people
                            </span>
                            <span style="background: #e8f5e8; color: #27ae60; padding: 5px 10px; border-radius: 15px; font-size: 0.9rem;">
                                Featured
                            </span>
                        </div>
                        
                        <div style="text-align: center;">
                            <a href="{{ url_for('classroom_details', classroom_id=classroom.id) }}" class="btn btn-primary">View Details</a>
                            {% if session.user_id %}
                                <a href="{{ url_for('book_classroom', classroom_id=classroom.id) }}" class="btn btn-success">Book Now</a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div style="text-align: center; padding: 2rem;">
            <p style="color: #7f8c8d; font-size: 1.1rem;">No featured classrooms available at the moment.</p>
        </div>
        {% endif %}
    </div>

    <!-- Sample Events Section -->
    <div class="events-section" style="margin-top: 3rem;">
        <h2 style="text-align: center; margin-bottom: 2rem; color: #2c3e50;">Sample Events You Can Book</h2>
        <div class="row">
            <div class="col-md-4">
                <div class="card" style="text-align: center; padding: 1.5rem;">
                    <h4 style="color: #e74c3c;">Lecture: Web Application Development</h4>
                    <p>Perfect for large lecture halls with projector facilities</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card" style="text-align: center; padding: 1.5rem;">
                    <h4 style="color: #f39c12;">Seminar: Data Structure and Algorithms</h4>
                    <p>Ideal for small group discussions and interactive sessions</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card" style="text-align: center; padding: 1.5rem;">
                    <h4 style="color: #9b59b6;">Workshop: Software Engineering</h4>
                    <p>Great for hands-on workshops and practical sessions</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    {% if not session.user_id %}
    <div style="text-align: center; margin-top: 3rem; padding: 2rem; background: #f8f9fa; border-radius: 10px;">
        <h3 style="margin-bottom: 1rem;">Get Started Today!</h3>
        <p style="margin-bottom: 2rem;">Join our classroom booking system to reserve your preferred spaces</p>
        <a href="{{ url_for('signup') }}" class="btn btn-primary" style="margin-right: 1rem;">Sign Up</a>
        <a href="{{ url_for('login') }}" class="btn btn-secondary">Login</a>
    </div>
    {% endif %}
</div>

<style>
.classroom-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.icon-location::before {
    content: "📍";
    margin-right: 5px;
}
</style>
{% endblock %}
