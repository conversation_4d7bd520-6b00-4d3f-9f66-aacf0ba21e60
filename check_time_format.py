#!/usr/bin/env python3
# Check time format in database

import mysql.connector

def check_time_format():
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='123456',
            database='classroom_booking',
            use_pure=True,
            auth_plugin='mysql_native_password'
        )
        
        cursor = connection.cursor(dictionary=True)
        
        # Check bookings table structure
        cursor.execute("DESCRIBE bookings")
        columns = cursor.fetchall()
        print("Bookings table structure:")
        for col in columns:
            print(f"  {col['Field']}: {col['Type']}")
        
        # Check sample booking data
        cursor.execute("SELECT * FROM bookings LIMIT 1")
        booking = cursor.fetchone()
        if booking:
            print(f"\nSample booking data:")
            for key, value in booking.items():
                print(f"  {key}: {value} (type: {type(value)})")
        else:
            print("\nNo bookings found in database")
        
        # Check time_slots table
        cursor.execute("SELECT * FROM time_slots LIMIT 1")
        time_slot = cursor.fetchone()
        if time_slot:
            print(f"\nSample time slot data:")
            for key, value in time_slot.items():
                print(f"  {key}: {value} (type: {type(value)})")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == '__main__':
    check_time_format()
