# Classroom Booking System - Main Flask Application
# This is the main entry point for the classroom booking system

from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify
import mysql.connector
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import os
from functools import wraps

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '123456',  # Update with your MySQL password
    'database': 'classroom_booking'
}

def get_db_connection():
    """Establish database connection"""
    try:
        connection = mysql.connector.connect(
            host=DB_CONFIG['host'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database=DB_CONFIG['database'],
            charset='utf8mb4',
            collation='utf8mb4_unicode_ci',
            autocommit=False,
            use_unicode=True
        )
        return connection
    except mysql.connector.Error as err:
        print(f"Database connection error: {err}")
        return None
    except Exception as e:
        print(f"Unexpected error: {e}")
        return None

def login_required(f):
    """Decorator to require login for certain routes"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page.', 'error')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    """Decorator to require admin privileges"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session or not session.get('is_admin', False):
            flash('Admin access required.', 'error')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

@app.route('/')
def home():
    """Home page route"""
    conn = get_db_connection()
    featured_classrooms = []

    if conn:
        cursor = conn.cursor(dictionary=True)
        cursor.execute("""
            SELECT * FROM classrooms
            WHERE is_featured = TRUE
            ORDER BY name
            LIMIT 4
        """)
        featured_classrooms = cursor.fetchall()
        cursor.close()
        conn.close()

    return render_template('home.html', featured_classrooms=featured_classrooms)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Login page route"""
    if request.method == 'POST':
        email = request.form['email']
        password = request.form['password']
        
        conn = get_db_connection()
        if conn:
            cursor = conn.cursor(dictionary=True)
            cursor.execute("SELECT * FROM users WHERE email = %s", (email,))
            user = cursor.fetchone()
            
            if user and check_password_hash(user['password'], password):
                session['user_id'] = user['id']
                session['user_name'] = user['full_name']
                session['is_admin'] = user['is_admin']
                
                if user['is_admin']:
                    return redirect(url_for('admin_dashboard'))
                else:
                    return redirect(url_for('user_dashboard'))
            else:
                flash('Invalid email or password.', 'error')
            
            cursor.close()
            conn.close()
    
    return render_template('login.html')

@app.route('/signup', methods=['GET', 'POST'])
def signup():
    """Signup page route"""
    if request.method == 'POST':
        full_name = request.form['full_name']
        email = request.form['email']
        password = request.form['password']
        confirm_password = request.form['confirm_password']
        
        if password != confirm_password:
            flash('Passwords do not match.', 'error')
            return render_template('signup.html')
        
        conn = get_db_connection()
        if conn:
            cursor = conn.cursor()
            
            # Check if email already exists
            cursor.execute("SELECT id FROM users WHERE email = %s", (email,))
            if cursor.fetchone():
                flash('Email already registered.', 'error')
                return render_template('signup.html')
            
            # Create new user
            hashed_password = generate_password_hash(password)
            cursor.execute(
                "INSERT INTO users (full_name, email, password, is_admin) VALUES (%s, %s, %s, %s)",
                (full_name, email, hashed_password, False)
            )
            conn.commit()
            
            flash('Account created successfully! Please log in.', 'success')
            cursor.close()
            conn.close()
            return redirect(url_for('login'))
    
    return render_template('signup.html')

@app.route('/logout')
def logout():
    """Logout route"""
    session.clear()
    flash('You have been logged out.', 'info')
    return redirect(url_for('home'))

@app.route('/classrooms')
def classrooms():
    """Classrooms listing page"""
    conn = get_db_connection()
    if conn:
        cursor = conn.cursor(dictionary=True)
        cursor.execute("""
            SELECT c.*,
                   GROUP_CONCAT(DISTINCT CONCAT(ts.day_of_week, ': ', ts.start_time, '-', ts.end_time)
                   ORDER BY FIELD(ts.day_of_week, 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday')
                   SEPARATOR '<br>') as available_times
            FROM classrooms c
            LEFT JOIN time_slots ts ON c.id = ts.classroom_id AND ts.is_available = TRUE
            GROUP BY c.id
            ORDER BY c.name
        """)
        classrooms_list = cursor.fetchall()
        cursor.close()
        conn.close()
        return render_template('classrooms.html', classrooms=classrooms_list)

    flash('Database connection error.', 'error')
    return redirect(url_for('home'))

@app.route('/classroom/<int:classroom_id>')
def classroom_details(classroom_id):
    """Classroom details page"""
    conn = get_db_connection()
    if conn:
        cursor = conn.cursor(dictionary=True)

        # Get classroom details
        cursor.execute("SELECT * FROM classrooms WHERE id = %s", (classroom_id,))
        classroom = cursor.fetchone()

        if not classroom:
            flash('Classroom not found.', 'error')
            return redirect(url_for('classrooms'))

        # Get available time slots
        cursor.execute("""
            SELECT day_of_week, start_time, end_time
            FROM time_slots
            WHERE classroom_id = %s AND is_available = TRUE
            ORDER BY FIELD(day_of_week, 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'), start_time
        """, (classroom_id,))
        time_slots = cursor.fetchall()

        cursor.close()
        conn.close()

        return render_template('classroom_details.html', classroom=classroom, time_slots=time_slots)

    flash('Database connection error.', 'error')
    return redirect(url_for('classrooms'))

@app.route('/book/<int:classroom_id>', methods=['GET', 'POST'])
@login_required
def book_classroom(classroom_id):
    """Booking page"""
    from datetime import date
    today = date.today()

    conn = get_db_connection()
    if not conn:
        flash('Database connection error.', 'error')
        return redirect(url_for('classrooms'))

    cursor = conn.cursor(dictionary=True)

    # Get classroom details
    cursor.execute("SELECT * FROM classrooms WHERE id = %s", (classroom_id,))
    classroom = cursor.fetchone()

    if not classroom:
        flash('Classroom not found.', 'error')
        return redirect(url_for('classrooms'))

    if request.method == 'POST':
        event_name = request.form['event_name']
        booking_date = request.form['booking_date']
        start_time = request.form['start_time']
        end_time = request.form['end_time']

        # Check for conflicts
        cursor.execute("""
            SELECT id FROM bookings
            WHERE classroom_id = %s AND booking_date = %s
            AND status = 'confirmed'
            AND ((start_time <= %s AND end_time > %s) OR (start_time < %s AND end_time >= %s))
        """, (classroom_id, booking_date, start_time, start_time, end_time, end_time))

        if cursor.fetchone():
            flash('Time slot is already booked. Please choose a different time.', 'error')
        else:
            # Create booking
            cursor.execute("""
                INSERT INTO bookings (user_id, classroom_id, event_name, booking_date, start_time, end_time)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (session['user_id'], classroom_id, event_name, booking_date, start_time, end_time))

            conn.commit()
            flash('Booking confirmed successfully!', 'success')
            cursor.close()
            conn.close()
            return redirect(url_for('user_dashboard'))

    cursor.close()
    conn.close()
    return render_template('book_classroom.html', classroom=classroom, today=today)

@app.route('/user-dashboard')
@login_required
def user_dashboard():
    """User dashboard page"""
    from datetime import date
    today = date.today()

    conn = get_db_connection()
    if conn:
        cursor = conn.cursor(dictionary=True)
        cursor.execute("""
            SELECT b.*, c.name as classroom_name, c.building_name, c.location
            FROM bookings b
            JOIN classrooms c ON b.classroom_id = c.id
            WHERE b.user_id = %s AND b.status = 'confirmed'
            ORDER BY b.booking_date, b.start_time
        """, (session['user_id'],))
        bookings = cursor.fetchall()
        cursor.close()
        conn.close()
        return render_template('user_dashboard.html', bookings=bookings, today=today)

    flash('Database connection error.', 'error')
    return redirect(url_for('home'))

@app.route('/cancel-booking/<int:booking_id>')
@login_required
def cancel_booking(booking_id):
    """Cancel a booking"""
    conn = get_db_connection()
    if conn:
        cursor = conn.cursor()
        cursor.execute("""
            UPDATE bookings SET status = 'cancelled'
            WHERE id = %s AND user_id = %s
        """, (booking_id, session['user_id']))

        if cursor.rowcount > 0:
            conn.commit()
            flash('Booking cancelled successfully.', 'success')
        else:
            flash('Booking not found or you do not have permission to cancel it.', 'error')

        cursor.close()
        conn.close()

    return redirect(url_for('user_dashboard'))

@app.route('/admin-dashboard')
@admin_required
def admin_dashboard():
    """Admin dashboard page"""
    from datetime import date
    today = date.today()

    conn = get_db_connection()
    if conn:
        cursor = conn.cursor(dictionary=True)

        # Get all classrooms
        cursor.execute("SELECT * FROM classrooms ORDER BY name")
        classrooms_list = cursor.fetchall()

        # Get all bookings
        cursor.execute("""
            SELECT b.*, u.full_name as user_name, c.name as classroom_name
            FROM bookings b
            JOIN users u ON b.user_id = u.id
            JOIN classrooms c ON b.classroom_id = c.id
            WHERE b.status = 'confirmed'
            ORDER BY b.booking_date, b.start_time
        """)
        bookings = cursor.fetchall()

        cursor.close()
        conn.close()
        return render_template('admin_dashboard.html', classrooms=classrooms_list, bookings=bookings, today=today)

    flash('Database connection error.', 'error')
    return redirect(url_for('home'))

@app.route('/admin/add-classroom', methods=['GET', 'POST'])
@admin_required
def add_classroom():
    """Add new classroom"""
    if request.method == 'POST':
        name = request.form['name']
        building_name = request.form['building_name']
        location = request.form['location']
        capacity = request.form['capacity']
        description = request.form.get('description', '')
        is_featured = 'is_featured' in request.form

        conn = get_db_connection()
        if conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO classrooms (name, building_name, location, capacity, description, is_featured)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (name, building_name, location, capacity, description, is_featured))

            classroom_id = cursor.lastrowid

            # Add default time slots
            time_slots = [
                ('09:00:00', '10:30:00'),
                ('10:45:00', '12:15:00'),
                ('13:30:00', '15:00:00'),
                ('15:15:00', '16:45:00'),
                ('17:00:00', '18:30:00')
            ]

            days_of_week = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']

            for day in days_of_week:
                for start_time, end_time in time_slots:
                    cursor.execute("""
                        INSERT INTO time_slots (classroom_id, day_of_week, start_time, end_time, is_available)
                        VALUES (%s, %s, %s, %s, %s)
                    """, (classroom_id, day, start_time, end_time, True))

            conn.commit()
            flash('Classroom added successfully!', 'success')
            cursor.close()
            conn.close()
            return redirect(url_for('admin_dashboard'))

    return render_template('add_classroom.html')

@app.route('/admin/edit-classroom/<int:classroom_id>', methods=['GET', 'POST'])
@admin_required
def edit_classroom(classroom_id):
    """Edit classroom"""
    conn = get_db_connection()
    if not conn:
        flash('Database connection error.', 'error')
        return redirect(url_for('admin_dashboard'))

    cursor = conn.cursor(dictionary=True)

    if request.method == 'POST':
        name = request.form['name']
        building_name = request.form['building_name']
        location = request.form['location']
        capacity = request.form['capacity']
        description = request.form.get('description', '')
        is_featured = 'is_featured' in request.form

        cursor.execute("""
            UPDATE classrooms
            SET name = %s, building_name = %s, location = %s, capacity = %s,
                description = %s, is_featured = %s
            WHERE id = %s
        """, (name, building_name, location, capacity, description, is_featured, classroom_id))

        conn.commit()
        flash('Classroom updated successfully!', 'success')
        cursor.close()
        conn.close()
        return redirect(url_for('admin_dashboard'))

    # GET request - show edit form
    cursor.execute("SELECT * FROM classrooms WHERE id = %s", (classroom_id,))
    classroom = cursor.fetchone()

    if not classroom:
        flash('Classroom not found.', 'error')
        return redirect(url_for('admin_dashboard'))

    cursor.close()
    conn.close()
    return render_template('edit_classroom.html', classroom=classroom)

@app.route('/admin/delete-classroom/<int:classroom_id>')
@admin_required
def delete_classroom(classroom_id):
    """Delete classroom"""
    conn = get_db_connection()
    if conn:
        cursor = conn.cursor()
        cursor.execute("DELETE FROM classrooms WHERE id = %s", (classroom_id,))

        if cursor.rowcount > 0:
            conn.commit()
            flash('Classroom deleted successfully!', 'success')
        else:
            flash('Classroom not found.', 'error')

        cursor.close()
        conn.close()

    return redirect(url_for('admin_dashboard'))

@app.route('/api/check-availability', methods=['POST'])
@login_required
def check_availability():
    """API endpoint to check classroom availability"""
    classroom_id = request.form.get('classroom_id')
    booking_date = request.form.get('booking_date')

    if not classroom_id or not booking_date:
        return jsonify({'success': False, 'error': 'Missing parameters'})

    conn = get_db_connection()
    if conn:
        cursor = conn.cursor(dictionary=True)

        # Get day of week
        from datetime import datetime
        date_obj = datetime.strptime(booking_date, '%Y-%m-%d')
        day_name = date_obj.strftime('%A')

        # Get available time slots for this day
        cursor.execute("""
            SELECT start_time, end_time FROM time_slots
            WHERE classroom_id = %s AND day_of_week = %s AND is_available = TRUE
        """, (classroom_id, day_name))

        available_slots = []
        for slot in cursor.fetchall():
            # Check if this time slot is already booked
            cursor.execute("""
                SELECT id FROM bookings
                WHERE classroom_id = %s AND booking_date = %s
                AND status = 'confirmed'
                AND start_time = %s AND end_time = %s
            """, (classroom_id, booking_date, slot['start_time'], slot['end_time']))

            if not cursor.fetchone():
                available_slots.append({
                    'start': str(slot['start_time']),
                    'end': str(slot['end_time'])
                })

        cursor.close()
        conn.close()

        return jsonify({'success': True, 'available_slots': available_slots})

    return jsonify({'success': False, 'error': 'Database connection error'})

if __name__ == '__main__':
    app.run(debug=True)
