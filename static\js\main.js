// Classroom Booking System - Main JavaScript File

$(document).ready(function() {
    // Auto-hide flash messages after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // Form validation for booking form
    $('#booking-form').on('submit', function(e) {
        const startTime = $('#start_time').val();
        const endTime = $('#end_time').val();
        const bookingDate = $('#booking_date').val();
        
        // Check if end time is after start time
        if (startTime && endTime && startTime >= endTime) {
            e.preventDefault();
            alert('End time must be after start time.');
            return false;
        }
        
        // Check if booking date is not in the past
        const today = new Date().toISOString().split('T')[0];
        if (bookingDate < today) {
            e.preventDefault();
            alert('Booking date cannot be in the past.');
            return false;
        }
    });

    // Confirm deletion actions
    $('.delete-btn').on('click', function(e) {
        if (!confirm('Are you sure you want to delete this item?')) {
            e.preventDefault();
            return false;
        }
    });

    // Confirm cancellation actions
    $('.cancel-btn').on('click', function(e) {
        if (!confirm('Are you sure you want to cancel this booking?')) {
            e.preventDefault();
            return false;
        }
    });

    // Search functionality for classrooms
    $('#classroom-search').on('keyup', function() {
        const searchTerm = $(this).val().toLowerCase();
        $('.classroom-card').each(function() {
            const classroomName = $(this).find('.classroom-name').text().toLowerCase();
            const buildingName = $(this).find('.building-name').text().toLowerCase();
            
            if (classroomName.includes(searchTerm) || buildingName.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // Dynamic time slot checking
    $('#booking_date, #classroom_id').on('change', function() {
        const classroomId = $('#classroom_id').val();
        const bookingDate = $('#booking_date').val();
        
        if (classroomId && bookingDate) {
            checkAvailableTimeSlots(classroomId, bookingDate);
        }
    });

    // Password confirmation validation
    $('#confirm_password').on('keyup', function() {
        const password = $('#password').val();
        const confirmPassword = $(this).val();
        
        if (password !== confirmPassword) {
            $(this).addClass('error');
            $('#password-match-error').show();
        } else {
            $(this).removeClass('error');
            $('#password-match-error').hide();
        }
    });

    // Mobile menu toggle
    $('.mobile-menu-toggle').on('click', function() {
        $('.navigation').toggleClass('active');
    });
});

// Function to check available time slots via AJAX
function checkAvailableTimeSlots(classroomId, bookingDate) {
    $.ajax({
        url: '/api/check-availability',
        method: 'POST',
        data: {
            classroom_id: classroomId,
            booking_date: bookingDate
        },
        success: function(response) {
            if (response.success) {
                updateTimeSlotOptions(response.available_slots);
            }
        },
        error: function() {
            console.error('Error checking availability');
        }
    });
}

// Function to update time slot options
function updateTimeSlotOptions(availableSlots) {
    const startTimeSelect = $('#start_time');
    const endTimeSelect = $('#end_time');
    
    // Clear existing options
    startTimeSelect.empty();
    endTimeSelect.empty();
    
    // Add default option
    startTimeSelect.append('<option value="">Select start time</option>');
    endTimeSelect.append('<option value="">Select end time</option>');
    
    // Add available time slots
    availableSlots.forEach(function(slot) {
        startTimeSelect.append(`<option value="${slot.start}">${slot.start}</option>`);
        endTimeSelect.append(`<option value="${slot.end}">${slot.end}</option>`);
    });
}

// Function to format date for display
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// Function to format time for display
function formatTime(timeString) {
    const time = new Date(`2000-01-01T${timeString}`);
    return time.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });
}
