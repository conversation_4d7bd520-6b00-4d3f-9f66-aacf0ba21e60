{% extends "base.html" %}

{% block title %}Book {{ classroom.name }} - Classroom Booking System{% endblock %}

{% block content %}
<div class="container">
    <!-- Breadcrumb -->
    <nav style="margin-bottom: 2rem;">
        <ol style="list-style: none; padding: 0; display: flex; gap: 10px; color: #7f8c8d;">
            <li><a href="{{ url_for('home') }}" style="color: #3498db; text-decoration: none;">Home</a></li>
            <li>::</li>
            <li><a href="{{ url_for('classrooms') }}" style="color: #3498db; text-decoration: none;">Classrooms</a></li>
            <li>::</li>
            <li><a href="{{ url_for('classroom_details', classroom_id=classroom.id) }}" style="color: #3498db; text-decoration: none;">{{ classroom.name }}</a></li>
            <li>::</li>
            <li style="color: #2c3e50;">Book</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Booking Form -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">Book a Classroom</h1>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('book_classroom', classroom_id=classroom.id) }}" id="booking-form">
                        <!-- User Information -->
                        <div class="user-info" style="background: #f8f9fa; padding: 1.5rem; border-radius: 8px; margin-bottom: 2rem;">
                            <h3 style="color: #2c3e50; margin-bottom: 1rem;">Your Information</h3>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="full_name">Full Name</label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" value="{{ session.user_name }}" readonly style="background: #ecf0f1;">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="email">Email</label>
                                        <input type="email" class="form-control" id="email" name="email" value="Contact admin for email" readonly style="background: #ecf0f1;">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="phone">Phone Number (Optional)</label>
                                <input type="tel" class="form-control" id="phone" name="phone" placeholder="Enter your phone number">
                            </div>
                        </div>

                        <!-- Event Information -->
                        <div class="event-info" style="margin-bottom: 2rem;">
                            <h3 style="color: #2c3e50; margin-bottom: 1rem;">Event Details</h3>
                            <div class="form-group">
                                <label for="event_name">Event Name</label>
                                <input type="text" class="form-control" id="event_name" name="event_name" required placeholder="e.g., Lecture Web Application Development, Seminar Data Structure and Algorithms">
                                <small class="form-text text-muted">Describe the purpose of your booking</small>
                            </div>
                        </div>

                        <!-- Booking Details -->
                        <div class="booking-details" style="margin-bottom: 2rem;">
                            <h3 style="color: #2c3e50; margin-bottom: 1rem;">Booking Details</h3>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="booking_date">Date</label>
                                        <input type="date" class="form-control" id="booking_date" name="booking_date" required min="{{ today }}">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="start_time">Start Time</label>
                                        <select class="form-control" id="start_time" name="start_time" required>
                                            <option value="">Select start time</option>
                                            <option value="09:00">09:00 AM</option>
                                            <option value="10:45">10:45 AM</option>
                                            <option value="13:30">01:30 PM</option>
                                            <option value="15:15">03:15 PM</option>
                                            <option value="17:00">05:00 PM</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="end_time">End Time</label>
                                        <select class="form-control" id="end_time" name="end_time" required>
                                            <option value="">Select end time</option>
                                            <option value="10:30">10:30 AM</option>
                                            <option value="12:15">12:15 PM</option>
                                            <option value="15:00">03:00 PM</option>
                                            <option value="16:45">04:45 PM</option>
                                            <option value="18:30">06:30 PM</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="form-actions" style="text-align: center; padding-top: 1rem; border-top: 1px solid #ecf0f1;">
                            <button type="submit" class="btn btn-success" style="font-size: 1.1rem; padding: 12px 30px; margin-right: 1rem;">
                                <i class="icon-confirm"></i> Confirm Booking
                            </button>
                            <a href="{{ url_for('classroom_details', classroom_id=classroom.id) }}" class="btn btn-secondary" style="font-size: 1.1rem; padding: 12px 30px;">
                                <i class="icon-cancel"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Classroom Summary -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Classroom Summary</h3>
                </div>
                <div class="card-body">
                    <div class="classroom-summary">
                        <h4 style="color: #3498db; margin-bottom: 1rem;">{{ classroom.name }}</h4>
                        
                        <div class="summary-item" style="margin-bottom: 1rem; padding-bottom: 1rem; border-bottom: 1px solid #ecf0f1;">
                            <strong style="color: #7f8c8d;">Building:</strong><br>
                            <span style="font-size: 1.1rem;">{{ classroom.building_name }}</span>
                        </div>
                        
                        {% if classroom.location %}
                        <div class="summary-item" style="margin-bottom: 1rem; padding-bottom: 1rem; border-bottom: 1px solid #ecf0f1;">
                            <strong style="color: #7f8c8d;">Location:</strong><br>
                            <span style="font-size: 1.1rem;">{{ classroom.location }}</span>
                        </div>
                        {% endif %}
                        
                        <div class="summary-item" style="margin-bottom: 1rem; padding-bottom: 1rem; border-bottom: 1px solid #ecf0f1;">
                            <strong style="color: #7f8c8d;">Capacity:</strong><br>
                            <span style="font-size: 1.1rem; color: #e74c3c; font-weight: bold;">{{ classroom.capacity }} people</span>
                        </div>
                        
                        {% if classroom.description %}
                        <div class="summary-item">
                            <strong style="color: #7f8c8d;">Description:</strong><br>
                            <span style="font-size: 0.95rem; line-height: 1.5;">{{ classroom.description }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Booking Guidelines -->
            <div class="card" style="margin-top: 1rem;">
                <div class="card-header">
                    <h3 class="card-title">Booking Guidelines</h3>
                </div>
                <div class="card-body">
                    <ul style="padding-left: 1.2rem; line-height: 1.6;">
                        <li>Bookings must be made at least 24 hours in advance</li>
                        <li>Maximum booking duration is 2 hours per session</li>
                        <li>Please arrive on time for your booking</li>
                        <li>Cancel bookings you cannot attend</li>
                        <li>Keep the classroom clean and tidy</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.icon-confirm::before { content: "✅"; margin-right: 8px; }
.icon-cancel::before { content: "❌"; margin-right: 8px; }

#booking-form .form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.btn-success:hover {
    background-color: #229954;
    transform: translateY(-1px);
}

.btn-secondary:hover {
    background-color: #7f8c8d;
    transform: translateY(-1px);
}

@media (max-width: 768px) {
    .form-actions .btn {
        width: 100%;
        margin-bottom: 10px;
        margin-right: 0 !important;
    }
}
</style>

<script>
// Set minimum date to today
document.getElementById('booking_date').min = new Date().toISOString().split('T')[0];

// Time slot validation
document.getElementById('start_time').addEventListener('change', function() {
    const startTime = this.value;
    const endTimeSelect = document.getElementById('end_time');
    
    // Clear end time selection
    endTimeSelect.value = '';
    
    // Enable/disable end time options based on start time
    Array.from(endTimeSelect.options).forEach(option => {
        if (option.value && option.value <= startTime) {
            option.disabled = true;
            option.style.color = '#ccc';
        } else {
            option.disabled = false;
            option.style.color = '';
        }
    });
});
</script>
{% endblock %}
