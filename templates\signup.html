{% extends "base.html" %}

{% block title %}Sign Up - Classroom Booking System{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-6" style="margin: 0 auto;">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">Sign Up</h2>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('signup') }}">
                        <div class="form-group">
                            <label for="full_name">Full Name</label>
                            <input type="text" class="form-control" id="full_name" name="full_name" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="password">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm_password">Confirm Password</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <div id="password-match-error" style="color: red; display: none;">Passwords do not match</div>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Sign Up</button>
                            <a href="{{ url_for('login') }}" class="btn btn-secondary">Back to Login</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
