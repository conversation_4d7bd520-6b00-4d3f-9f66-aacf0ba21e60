{% extends "base.html" %}

{% block title %}Your Dashboard - Classroom Booking System{% endblock %}

{% block content %}
<div class="container">
    <!-- Dashboard Header -->
    <div class="dashboard-header" style="text-align: center; margin-bottom: 3rem;">
        <h1 style="color: #2c3e50; margin-bottom: 0.5rem;">Your Dashboard</h1>
        <p style="color: #7f8c8d; font-size: 1.1rem;">Welcome back, {{ session.user_name }}!</p>
    </div>

    <!-- Quick Stats -->
    <div class="quick-stats" style="margin-bottom: 3rem;">
        <div class="row">
            <div class="col-md-4">
                <div class="stat-card" style="background: linear-gradient(135deg, #3498db, #2980b9); color: white; padding: 2rem; border-radius: 10px; text-align: center;">
                    <h3 style="margin: 0; font-size: 2rem;">{{ bookings|length }}</h3>
                    <p style="margin: 0; opacity: 0.9;">Total Bookings</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card" style="background: linear-gradient(135deg, #27ae60, #229954); color: white; padding: 2rem; border-radius: 10px; text-align: center;">
                    <h3 style="margin: 0; font-size: 2rem;">
                        {% set upcoming_count = bookings|selectattr('booking_date', 'ge', today)|list|length %}
                        {{ upcoming_count }}
                    </h3>
                    <p style="margin: 0; opacity: 0.9;">Upcoming Bookings</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card" style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white; padding: 2rem; border-radius: 10px; text-align: center;">
                    <h3 style="margin: 0; font-size: 2rem;">
                        {% set past_count = bookings|selectattr('booking_date', 'lt', today)|list|length %}
                        {{ past_count }}
                    </h3>
                    <p style="margin: 0; opacity: 0.9;">Past Bookings</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions" style="text-align: center; margin-bottom: 3rem;">
        <a href="{{ url_for('classrooms') }}" class="btn btn-primary" style="margin-right: 1rem; font-size: 1.1rem; padding: 12px 25px;">
            <i class="icon-book"></i> Book New Classroom
        </a>
        <a href="{{ url_for('home') }}" class="btn btn-secondary" style="font-size: 1.1rem; padding: 12px 25px;">
            <i class="icon-home"></i> Back to Home
        </a>
    </div>

    <!-- Upcoming Bookings Section -->
    <div class="bookings-section">
        <h2 style="color: #2c3e50; margin-bottom: 2rem; padding-bottom: 0.5rem; border-bottom: 3px solid #3498db;">
            <i class="icon-calendar"></i> Upcoming Bookings
        </h2>

        {% if bookings %}
            {% set upcoming_bookings = bookings|selectattr('booking_date', 'ge', today)|list %}
            {% if upcoming_bookings %}
                <div class="bookings-grid">
                    {% for booking in upcoming_bookings %}
                    <div class="booking-card" style="background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 1.5rem; overflow: hidden; transition: transform 0.3s ease;">
                        <div class="booking-header" style="background: linear-gradient(135deg, #3498db, #2980b9); color: white; padding: 1rem;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <h4 style="margin: 0; font-size: 1.2rem;">{{ booking.classroom_name }}</h4>
                                <span class="status-badge" style="background: rgba(255,255,255,0.2); padding: 4px 12px; border-radius: 15px; font-size: 0.8rem;">
                                    Confirmed
                                </span>
                            </div>
                        </div>
                        
                        <div class="booking-body" style="padding: 1.5rem;">
                            <div class="booking-details" style="margin-bottom: 1.5rem;">
                                <div class="detail-row" style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                    <span style="color: #7f8c8d; font-weight: 500;">Event:</span>
                                    <span style="font-weight: bold; color: #2c3e50;">{{ booking.event_name }}</span>
                                </div>
                                <div class="detail-row" style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                    <span style="color: #7f8c8d; font-weight: 500;">Building:</span>
                                    <span>{{ booking.building_name }}</span>
                                </div>
                                {% if booking.location %}
                                <div class="detail-row" style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                    <span style="color: #7f8c8d; font-weight: 500;">Location:</span>
                                    <span>{{ booking.location }}</span>
                                </div>
                                {% endif %}
                                <div class="detail-row" style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                    <span style="color: #7f8c8d; font-weight: 500;">Date:</span>
                                    <span style="font-weight: bold; color: #e74c3c;">{{ booking.booking_date.strftime('%A, %B %d, %Y') }}</span>
                                </div>
                                <div class="detail-row" style="display: flex; justify-content: space-between;">
                                    <span style="color: #7f8c8d; font-weight: 500;">Time:</span>
                                    <span style="font-weight: bold; color: #27ae60;">
                                        {{ booking.start_time|format_time }} - {{ booking.end_time|format_time }}
                                    </span>
                                </div>
                            </div>
                            
                            <div class="booking-actions" style="text-align: center; padding-top: 1rem; border-top: 1px solid #ecf0f1;">
                                <a href="{{ url_for('classroom_details', classroom_id=booking.classroom_id) }}" class="btn btn-primary" style="margin-right: 10px;">
                                    <i class="icon-view"></i> View Classroom
                                </a>
                                <a href="{{ url_for('cancel_booking', booking_id=booking.id) }}" class="btn btn-danger cancel-btn">
                                    <i class="icon-cancel"></i> Cancel Booking
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="no-bookings" style="text-align: center; padding: 3rem; background: #f8f9fa; border-radius: 10px;">
                    <div style="font-size: 4rem; margin-bottom: 1rem;">📅</div>
                    <h3 style="color: #7f8c8d; margin-bottom: 1rem;">No Upcoming Bookings</h3>
                    <p style="color: #95a5a6; margin-bottom: 2rem;">You don't have any upcoming classroom bookings.</p>
                    <a href="{{ url_for('classrooms') }}" class="btn btn-primary">Book a Classroom</a>
                </div>
            {% endif %}
        {% else %}
            <div class="no-bookings" style="text-align: center; padding: 3rem; background: #f8f9fa; border-radius: 10px;">
                <div style="font-size: 4rem; margin-bottom: 1rem;">📅</div>
                <h3 style="color: #7f8c8d; margin-bottom: 1rem;">No Bookings Yet</h3>
                <p style="color: #95a5a6; margin-bottom: 2rem;">Start by booking your first classroom!</p>
                <a href="{{ url_for('classrooms') }}" class="btn btn-primary">Browse Classrooms</a>
            </div>
        {% endif %}

        <!-- Past Bookings Section -->
        {% if bookings %}
            {% set past_bookings = bookings|selectattr('booking_date', 'lt', today)|list %}
            {% if past_bookings %}
                <div style="margin-top: 3rem;">
                    <h3 style="color: #7f8c8d; margin-bottom: 1.5rem;">Past Bookings</h3>
                    <div class="past-bookings" style="background: #f8f9fa; border-radius: 10px; padding: 1.5rem;">
                        {% for booking in past_bookings[:5] %}
                        <div class="past-booking-item" style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #ecf0f1;">
                            <div>
                                <strong>{{ booking.classroom_name }}</strong> - {{ booking.event_name }}
                                <br>
                                <small style="color: #7f8c8d;">{{ booking.booking_date.strftime('%B %d, %Y') }} at {{ booking.start_time|format_time }}</small>
                            </div>
                            <span style="color: #95a5a6; font-size: 0.9rem;">Completed</span>
                        </div>
                        {% endfor %}
                        {% if past_bookings|length > 5 %}
                        <div style="text-align: center; margin-top: 1rem;">
                            <small style="color: #7f8c8d;">And {{ past_bookings|length - 5 }} more...</small>
                        </div>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        {% endif %}
    </div>
</div>

<style>
.icon-book::before { content: "📚"; margin-right: 8px; }
.icon-home::before { content: "🏠"; margin-right: 8px; }
.icon-calendar::before { content: "📅"; margin-right: 8px; }
.icon-view::before { content: "👁️"; margin-right: 8px; }
.icon-cancel::before { content: "❌"; margin-right: 8px; }

.booking-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.stat-card {
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .detail-row {
        flex-direction: column !important;
        text-align: center;
    }
    
    .booking-actions .btn {
        width: 100%;
        margin-bottom: 10px;
        margin-right: 0 !important;
    }
    
    .quick-actions .btn {
        width: 100%;
        margin-bottom: 10px;
        margin-right: 0 !important;
    }
}
</style>
{% endblock %}
