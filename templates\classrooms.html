{% extends "base.html" %}

{% block title %}Available Classrooms - Classroom Booking System{% endblock %}

{% block content %}
<div class="container">
    <div class="page-header" style="text-align: center; margin-bottom: 2rem;">
        <h1 style="color: #2c3e50; margin-bottom: 1rem;">Available Classrooms</h1>
        <p style="color: #7f8c8d; font-size: 1.1rem;">Browse and book from our selection of classrooms</p>
    </div>

    <!-- Search Bar -->
    <div class="search-section" style="margin-bottom: 2rem;">
        <div class="row">
            <div class="col-md-6" style="margin: 0 auto;">
                <div class="form-group">
                    <input type="text" id="classroom-search" class="form-control" placeholder="Search classrooms by name or building..." style="font-size: 1.1rem; padding: 12px;">
                </div>
            </div>
        </div>
    </div>

    <!-- Classrooms Grid -->
    {% if classrooms %}
    <div class="classrooms-grid">
        <div class="row">
            {% for classroom in classrooms %}
            <div class="col-md-6" style="margin-bottom: 2rem;">
                <div class="card classroom-card" style="height: 100%; transition: transform 0.3s ease;">
                    <div class="card-body">
                        <div class="classroom-header" style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1rem;">
                            <div>
                                <h3 class="classroom-name" style="color: #3498db; margin-bottom: 0.5rem;">{{ classroom.name }}</h3>
                                <p class="building-name" style="color: #7f8c8d; margin: 0;">
                                    <i class="icon-location"></i> {{ classroom.building_name }}
                                </p>
                                {% if classroom.location %}
                                <p style="color: #95a5a6; font-size: 0.9rem; margin: 0;">{{ classroom.location }}</p>
                                {% endif %}
                            </div>
                            {% if classroom.is_featured %}
                            <span class="featured-badge" style="background: #e8f5e8; color: #27ae60; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem;">Featured</span>
                            {% endif %}
                        </div>

                        {% if classroom.description %}
                        <p style="color: #555; margin-bottom: 1rem;">{{ classroom.description }}</p>
                        {% endif %}

                        <div class="classroom-info" style="margin-bottom: 1.5rem;">
                            <div class="capacity-info" style="background: #ecf0f1; padding: 10px; border-radius: 8px; margin-bottom: 1rem;">
                                <strong style="color: #2c3e50;">Capacity:</strong> 
                                <span style="color: #e74c3c; font-weight: bold;">{{ classroom.capacity }} people</span>
                            </div>

                            {% if classroom.available_times %}
                            <div class="time-slots">
                                <h5 style="color: #2c3e50; margin-bottom: 0.5rem;">Available Time Slots:</h5>
                                <div style="background: #f8f9fa; padding: 10px; border-radius: 8px; font-size: 0.9rem; line-height: 1.6;">
                                    {{ classroom.available_times|safe }}
                                </div>
                            </div>
                            {% endif %}
                        </div>

                        <div class="classroom-actions" style="text-align: center;">
                            <a href="{{ url_for('classroom_details', classroom_id=classroom.id) }}" class="btn btn-primary" style="margin-right: 10px;">View Details</a>
                            {% if session.user_id %}
                                <a href="{{ url_for('book_classroom', classroom_id=classroom.id) }}" class="btn btn-success">Book Now</a>
                            {% else %}
                                <a href="{{ url_for('login') }}" class="btn btn-secondary">Login to Book</a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% else %}
    <div class="no-classrooms" style="text-align: center; padding: 3rem;">
        <h3 style="color: #7f8c8d;">No classrooms available</h3>
        <p style="color: #95a5a6;">Please check back later or contact the administrator.</p>
    </div>
    {% endif %}

    <!-- Back to Home -->
    <div style="text-align: center; margin-top: 3rem;">
        <a href="{{ url_for('home') }}" class="btn btn-secondary">Back to Home</a>
    </div>
</div>

<style>
.classroom-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.icon-location::before {
    content: "📍";
    margin-right: 5px;
}

.search-section input:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.featured-badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@media (max-width: 768px) {
    .classroom-header {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .featured-badge {
        margin-top: 10px;
    }
}
</style>
{% endblock %}
