# Database Setup Script for Classroom Booking System
# This script creates the database and tables, and inserts sample data

import mysql.connector
from werkzeug.security import generate_password_hash

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',  # Update with your MySQL password
}

def create_database():
    """Create the database if it doesn't exist"""
    try:
        connection = mysql.connector.connect(
            host=DB_CONFIG['host'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password']
        )
        cursor = connection.cursor()
        
        # Create database
        cursor.execute("CREATE DATABASE IF NOT EXISTS classroom_booking")
        print("Database 'classroom_booking' created successfully!")
        
        cursor.close()
        connection.close()
        
    except mysql.connector.Error as err:
        print(f"Error creating database: {err}")

def create_tables():
    """Create all necessary tables"""
    try:
        connection = mysql.connector.connect(
            host=DB_CONFIG['host'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database='classroom_booking'
        )
        cursor = connection.cursor()
        
        # Users table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                full_name VARCHAR(100) NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                phone VARCHAR(20),
                is_admin BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Classrooms table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS classrooms (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                building_name VARCHAR(100) NOT NULL,
                location VARCHAR(150),
                capacity INT NOT NULL,
                description TEXT,
                is_featured BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Time slots table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS time_slots (
                id INT AUTO_INCREMENT PRIMARY KEY,
                classroom_id INT,
                day_of_week ENUM('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'),
                start_time TIME NOT NULL,
                end_time TIME NOT NULL,
                is_available BOOLEAN DEFAULT TRUE,
                FOREIGN KEY (classroom_id) REFERENCES classrooms(id) ON DELETE CASCADE
            )
        """)
        
        # Bookings table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS bookings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT,
                classroom_id INT,
                event_name VARCHAR(200) NOT NULL,
                booking_date DATE NOT NULL,
                start_time TIME NOT NULL,
                end_time TIME NOT NULL,
                status ENUM('confirmed', 'cancelled') DEFAULT 'confirmed',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (classroom_id) REFERENCES classrooms(id) ON DELETE CASCADE
            )
        """)
        
        connection.commit()
        print("All tables created successfully!")
        
        cursor.close()
        connection.close()
        
    except mysql.connector.Error as err:
        print(f"Error creating tables: {err}")

def insert_sample_data():
    """Insert sample data for testing"""
    try:
        connection = mysql.connector.connect(
            host=DB_CONFIG['host'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database='classroom_booking'
        )
        cursor = connection.cursor()

        # Insert admin user
        admin_password = generate_password_hash('admin123')
        cursor.execute("""
            INSERT IGNORE INTO users (full_name, email, password, is_admin)
            VALUES (%s, %s, %s, %s)
        """, ('Admin User', '<EMAIL>', admin_password, True))

        # Insert sample regular users
        user_password = generate_password_hash('user123')
        cursor.execute("""
            INSERT IGNORE INTO users (full_name, email, password, phone, is_admin)
            VALUES (%s, %s, %s, %s, %s)
        """, ('John Smith', '<EMAIL>', user_password, '+1234567890', False))

        cursor.execute("""
            INSERT IGNORE INTO users (full_name, email, password, phone, is_admin)
            VALUES (%s, %s, %s, %s, %s)
        """, ('Jane Doe', '<EMAIL>', user_password, '+0987654321', False))

        # Insert sample classrooms
        classrooms_data = [
            ('Lecture Hall A', 'Main Building', 'Building A, Room 101', 150, 'Large lecture hall with projector and audio system', True),
            ('Computer Lab 1', 'Technology Building', 'Building B, Room 201', 30, 'Computer lab with 30 workstations', True),
            ('Seminar Room C', 'Academic Building', 'Building C, Room 301', 25, 'Small seminar room for group discussions', True),
            ('Conference Room D', 'Administration Building', 'Building D, Room 401', 50, 'Conference room with video conferencing facilities', True),
            ('Workshop Room E', 'Engineering Building', 'Building E, Room 501', 40, 'Workshop room with tools and equipment', False),
            ('Study Room F', 'Library Building', 'Building F, Room 601', 12, 'Quiet study room for small groups', False)
        ]

        for classroom in classrooms_data:
            cursor.execute("""
                INSERT IGNORE INTO classrooms (name, building_name, location, capacity, description, is_featured)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, classroom)

        # Insert sample time slots for each classroom
        cursor.execute("SELECT id FROM classrooms")
        classroom_ids = [row[0] for row in cursor.fetchall()]

        # Standard time slots for weekdays
        time_slots = [
            ('09:00:00', '10:30:00'),
            ('10:45:00', '12:15:00'),
            ('13:30:00', '15:00:00'),
            ('15:15:00', '16:45:00'),
            ('17:00:00', '18:30:00')
        ]

        days_of_week = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']

        for classroom_id in classroom_ids:
            for day in days_of_week:
                for start_time, end_time in time_slots:
                    cursor.execute("""
                        INSERT IGNORE INTO time_slots (classroom_id, day_of_week, start_time, end_time, is_available)
                        VALUES (%s, %s, %s, %s, %s)
                    """, (classroom_id, day, start_time, end_time, True))

        connection.commit()
        print("Sample data inserted successfully!")

        cursor.close()
        connection.close()

    except mysql.connector.Error as err:
        print(f"Error inserting sample data: {err}")

if __name__ == '__main__':
    print("Setting up Classroom Booking System database...")
    create_database()
    create_tables()
    insert_sample_data()
    print("Database setup completed!")
