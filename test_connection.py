#!/usr/bin/env python3
# Test database connection

import mysql.connector

def test_connection():
    try:
        print("Testing database connection...")
        
        # Test connection
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='123456',
            database='classroom_booking'
        )
        
        print("✓ Connection successful!")
        
        # Test query
        cursor = connection.cursor(dictionary=True)
        cursor.execute("SELECT COUNT(*) as count FROM classrooms")
        result = cursor.fetchone()
        print(f"✓ Found {result['count']} classrooms in database")
        
        # Test featured classrooms
        cursor.execute("SELECT * FROM classrooms WHERE is_featured = TRUE LIMIT 3")
        featured = cursor.fetchall()
        print(f"✓ Found {len(featured)} featured classrooms:")
        for classroom in featured:
            print(f"  - {classroom['name']} ({classroom['building_name']})")
        
        cursor.close()
        connection.close()
        print("✓ Connection closed successfully")
        return True
        
    except mysql.connector.Error as err:
        print(f"❌ MySQL Error: {err}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == '__main__':
    if test_connection():
        print("\n🎉 Database connection is working!")
    else:
        print("\n💥 Database connection failed!")
