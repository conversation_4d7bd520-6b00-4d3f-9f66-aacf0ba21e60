#!/usr/bin/env python3
# Test database connection with different methods

import mysql.connector
import sys

def test_connection_method1():
    """Test with basic connection"""
    try:
        print("Method 1: Basic connection...")
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='123456',
            database='classroom_booking'
        )
        print("✓ Method 1 successful!")
        connection.close()
        return True
    except Exception as e:
        print(f"❌ Method 1 failed: {e}")
        return False

def test_connection_method2():
    """Test with explicit parameters"""
    try:
        print("Method 2: Connection with explicit parameters...")
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='123456',
            database='classroom_booking',
            auth_plugin='mysql_native_password'
        )
        print("✓ Method 2 successful!")
        connection.close()
        return True
    except Exception as e:
        print(f"❌ Method 2 failed: {e}")
        return False

def test_connection_method3():
    """Test with connection pooling disabled"""
    try:
        print("Method 3: Connection without pooling...")
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='123456',
            database='classroom_booking',
            use_pure=True
        )
        print("✓ Method 3 successful!")
        connection.close()
        return True
    except Exception as e:
        print(f"❌ Method 3 failed: {e}")
        return False

def test_database_exists():
    """Test if database exists"""
    try:
        print("Testing if database exists...")
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='123456'
        )
        cursor = connection.cursor()
        cursor.execute("SHOW DATABASES LIKE 'classroom_booking'")
        result = cursor.fetchone()
        if result:
            print("✓ Database 'classroom_booking' exists")
            return True
        else:
            print("❌ Database 'classroom_booking' does not exist")
            return False
    except Exception as e:
        print(f"❌ Database check failed: {e}")
        return False

if __name__ == '__main__':
    print("Testing MySQL connection with different methods...\n")

    # Test if database exists first
    if not test_database_exists():
        print("\nPlease run 'python database_setup.py' first to create the database.")
        sys.exit(1)

    # Try different connection methods
    methods = [test_connection_method1, test_connection_method2, test_connection_method3]

    for i, method in enumerate(methods, 1):
        if method():
            print(f"\n🎉 Connection method {i} works! Using this for the application.")
            break
        print()
    else:
        print("\n💥 All connection methods failed!")
        print("Please check your MySQL installation and credentials.")
